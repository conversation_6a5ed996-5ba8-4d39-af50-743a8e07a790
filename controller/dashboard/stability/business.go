package stability

import (
	"github.com/gin-gonic/gin"

	"dt-common/gintool"
	"stabilityDigitalBase/library/ent/contact"
	"stabilityDigitalBase/library/errs"
	"stabilityDigitalBase/library/mysql"
)

type TeamBusiness struct {
	Team     string `json:"team"`
	Business string `json:"business"`
}

// TeamBusinesses 业务团队结构
type TeamBusinesses struct {
	Team     string   `json:"team"`
	Business []string `json:"business"`
}

// GetBusiness 获取业务团队下拉列表
func GetBusiness(c *gin.Context) {
	db, err := mysql.Database()
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}

	// 查询所有业务团队信息，去重
	var results []*TeamBusiness
	ctx, cancel := mysql.ContextWithTimeout()
	err = db.Contact.Query().
		Select(contact.FieldTeam, contact.FieldBusiness).
		GroupBy(contact.FieldTeam, contact.FieldBusiness).
		Scan(ctx, &results)
	cancel()
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}

	bizMap := map[string][]string{}
	for _, item := range results {
		bizMap[item.Team] = append(bizMap[item.Team], item.Business)
	}

	var respBody []*TeamBusinesses
	for k, v := range bizMap {
		respBody = append(respBody, &TeamBusinesses{Team: k, Business: v})
	}

	gintool.JSON2FE(c, respBody, nil)
}
