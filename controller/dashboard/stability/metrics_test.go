package stability

import (
	"encoding/json"
	"io"
	"net/http/httptest"
	"net/url"
	"sort"
	"strconv"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"

	"stabilityDigitalBase/env"
)

// TestGetOperationMetric 测试获取操作风险类指标
func TestGetOperationMetric(t *testing.T) {
	env.<PERSON>ck(t, "../../config/config.yaml")

	type args struct {
		c *gin.Context
		w *httptest.ResponseRecorder
	}
	tests := []struct {
		name   string
		before func()
		args   args
		expect func(*testing.T, *httptest.ResponseRecorder)
	}{
		{
			name: "test: 参数验证 - 缺少参数",
			before: func() {
				// 不需要准备数据
			},
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				c.Request = httptest.NewRequest("GET", "/dashboard/metric/operation", nil)
				q := url.Values{}
				q.Add("team", "信贷团队")
				q.Add("business", "信贷稳定性1")
				// 缺少 startMonth 和 endMonth 参数
				c.Request.URL.RawQuery = q.Encode()
				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]json.RawMessage
				json.Unmarshal(b, &resp)
				if string(resp["code"]) == "0" {
					t.Error("应该返回参数错误")
				}
			},
		},
		{
			name: "test: 时间格式验证",
			before: func() {
				// 不需要准备数据
			},
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				c.Request = httptest.NewRequest("GET", "/dashboard/metric/operation", nil)
				q := url.Values{}
				q.Add("team", "信贷团队")
				q.Add("business", "信贷稳定性1")
				q.Add("startMonth", "2025-13") // 错误的月份格式
				q.Add("endMonth", "2025-08")
				c.Request.URL.RawQuery = q.Encode()
				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]json.RawMessage
				json.Unmarshal(b, &resp)
				if string(resp["code"]) == "0" {
					t.Error("应该返回时间格式错误")
				}
			},
		},
		{
			name: "test: 正常查询",
			before: func() {
				// 不需要准备数据
			},
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				c.Request = httptest.NewRequest("GET", "/dashboard/metric/operation", nil)
				q := url.Values{}
				q.Add("team", "信贷团队")
				q.Add("business", "信贷稳定性1")
				q.Add("startMonth", "2025-08")
				q.Add("endMonth", "2025-08")
				c.Request.URL.RawQuery = q.Encode()
				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]json.RawMessage
				json.Unmarshal(b, &resp)
				// 正常情况下应该返回成功，即使没有数据
				if string(resp["code"]) != "0" {
					t.Error("正常查询应该返回成功")
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			GetOperationMetric(tt.args.c)
			if tt.expect != nil {
				tt.expect(t, tt.args.w)
			}
		})
	}
}

// TestGetCapacityMetric 测试获取容量类指标
func TestGetCapacityMetric(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")

	type args struct {
		c *gin.Context
		w *httptest.ResponseRecorder
	}
	tests := []struct {
		name   string
		before func()
		args   args
		expect func(*testing.T, *httptest.ResponseRecorder)
	}{
		{
			name: "test: 参数验证 - 缺少参数",
			before: func() {
				// 不需要准备数据
			},
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				c.Request = httptest.NewRequest("GET", "/dashboard/metric/capacity", nil)
				q := url.Values{}
				q.Add("team", "信贷团队")
				q.Add("business", "信贷稳定性1")
				// 缺少 startMonth 和 endMonth 参数
				c.Request.URL.RawQuery = q.Encode()
				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]json.RawMessage
				json.Unmarshal(b, &resp)
				if string(resp["code"]) == "0" {
					t.Error("应该返回参数错误")
				}
			},
		},
		{
			name: "test: 时间格式验证",
			before: func() {
				// 不需要准备数据
			},
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				c.Request = httptest.NewRequest("GET", "/dashboard/metric/capacity", nil)
				q := url.Values{}
				q.Add("team", "信贷团队")
				q.Add("business", "信贷稳定性1")
				q.Add("startMonth", "2025-13") // 错误的月份格式
				q.Add("endMonth", "2025-08")
				c.Request.URL.RawQuery = q.Encode()
				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]json.RawMessage
				json.Unmarshal(b, &resp)
				if string(resp["code"]) == "0" {
					t.Error("应该返回时间格式错误")
				}
			},
		},
		{
			name: "test: 正常查询",
			before: func() {
				// 不需要准备数据
			},
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				c.Request = httptest.NewRequest("GET", "/dashboard/metric/capacity", nil)
				q := url.Values{}
				q.Add("team", "信贷团队")
				q.Add("business", "信贷稳定性1")
				q.Add("startMonth", "2025-08")
				q.Add("endMonth", "2025-08")
				c.Request.URL.RawQuery = q.Encode()
				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]json.RawMessage
				json.Unmarshal(b, &resp)
				// 正常情况下应该返回成功，即使没有数据
				if string(resp["code"]) != "0" {
					t.Error("正常查询应该返回成功")
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			GetCapacityMetric(tt.args.c)
			if tt.expect != nil {
				tt.expect(t, tt.args.w)
			}
		})
	}
}

// TestGetDeploymentMetric 测试获取部署类指标
func TestGetDeploymentMetric(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")

	type args struct {
		c *gin.Context
		w *httptest.ResponseRecorder
	}
	tests := []struct {
		name   string
		before func()
		args   args
		expect func(*testing.T, *httptest.ResponseRecorder)
	}{
		{
			name: "test: 参数验证 - 缺少参数",
			before: func() {
				// 不需要准备数据
			},
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				c.Request = httptest.NewRequest("GET", "/dashboard/metric/deployment", nil)
				q := url.Values{}
				q.Add("team", "信贷团队")
				q.Add("business", "信贷稳定性1")
				// 缺少 startMonth 和 endMonth 参数
				c.Request.URL.RawQuery = q.Encode()
				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]json.RawMessage
				json.Unmarshal(b, &resp)
				if string(resp["code"]) == "0" {
					t.Error("应该返回参数错误")
				}
			},
		},
		{
			name: "test: 时间格式验证",
			before: func() {
				// 不需要准备数据
			},
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				c.Request = httptest.NewRequest("GET", "/dashboard/metric/deployment", nil)
				q := url.Values{}
				q.Add("team", "信贷团队")
				q.Add("business", "信贷稳定性1")
				q.Add("startMonth", "2025-13") // 错误的月份格式
				q.Add("endMonth", "2025-08")
				c.Request.URL.RawQuery = q.Encode()
				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]json.RawMessage
				json.Unmarshal(b, &resp)
				if string(resp["code"]) == "0" {
					t.Error("应该返回时间格式错误")
				}
			},
		},
		{
			name: "test: 正常查询",
			before: func() {
				// 不需要准备数据
			},
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				c.Request = httptest.NewRequest("GET", "/dashboard/metric/deployment", nil)
				q := url.Values{}
				q.Add("team", "信贷团队")
				q.Add("business", "信贷稳定性1")
				q.Add("startMonth", "2025-08")
				q.Add("endMonth", "2025-08")
				c.Request.URL.RawQuery = q.Encode()
				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]json.RawMessage
				json.Unmarshal(b, &resp)
				// 正常情况下应该返回成功，即使没有数据
				if string(resp["code"]) != "0" {
					t.Error("正常查询应该返回成功")
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			GetDeploymentMetric(tt.args.c)
			if tt.expect != nil {
				tt.expect(t, tt.args.w)
			}
		})
	}
}

// TestGetMonitorMetric 测试获取监控类指标
func TestGetMonitorMetric(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")

	type args struct {
		c *gin.Context
		w *httptest.ResponseRecorder
	}
	tests := []struct {
		name   string
		before func()
		args   args
		expect func(*testing.T, *httptest.ResponseRecorder)
	}{
		{
			name: "test: 参数验证 - 缺少参数",
			before: func() {
				// 不需要准备数据
			},
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				c.Request = httptest.NewRequest("GET", "/dashboard/metric/monitor", nil)
				q := url.Values{}
				q.Add("team", "信贷团队")
				q.Add("business", "信贷稳定性1")
				// 缺少 startMonth 和 endMonth 参数
				c.Request.URL.RawQuery = q.Encode()
				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]json.RawMessage
				json.Unmarshal(b, &resp)
				if string(resp["code"]) == "0" {
					t.Error("应该返回参数错误")
				}
			},
		},
		{
			name: "test: 时间格式验证",
			before: func() {
				// 不需要准备数据
			},
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				c.Request = httptest.NewRequest("GET", "/dashboard/metric/monitor", nil)
				q := url.Values{}
				q.Add("team", "信贷团队")
				q.Add("business", "信贷稳定性1")
				q.Add("startMonth", "2025-13") // 错误的月份格式
				q.Add("endMonth", "2025-08")
				c.Request.URL.RawQuery = q.Encode()
				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]json.RawMessage
				json.Unmarshal(b, &resp)
				if string(resp["code"]) == "0" {
					t.Error("应该返回时间格式错误")
				}
			},
		},
		{
			name: "test: 正常查询",
			before: func() {
				// 不需要准备数据
			},
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				c.Request = httptest.NewRequest("GET", "/dashboard/metric/monitor", nil)
				q := url.Values{}
				q.Add("team", "信贷团队")
				q.Add("business", "信贷稳定性1")
				q.Add("startMonth", "2025-08")
				q.Add("endMonth", "2025-08")
				c.Request.URL.RawQuery = q.Encode()
				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]json.RawMessage
				json.Unmarshal(b, &resp)
				// 正常情况下应该返回成功，即使没有数据
				if string(resp["code"]) != "0" {
					t.Error("正常查询应该返回成功")
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			GetMonitorMetric(tt.args.c)
			if tt.expect != nil {
				tt.expect(t, tt.args.w)
			}
		})
	}
}

// TestGetPlanMetric 测试获取预案平台类指标
func TestGetPlanMetric(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")

	type args struct {
		c *gin.Context
		w *httptest.ResponseRecorder
	}
	tests := []struct {
		name   string
		before func()
		args   args
		expect func(*testing.T, *httptest.ResponseRecorder)
	}{
		{
			name: "test: 参数验证 - 缺少参数",
			before: func() {
				// 不需要准备数据
			},
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				c.Request = httptest.NewRequest("GET", "/dashboard/metric/plan", nil)
				q := url.Values{}
				q.Add("team", "信贷团队")
				q.Add("business", "信贷稳定性1")
				// 缺少 startMonth 和 endMonth 参数
				c.Request.URL.RawQuery = q.Encode()
				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]json.RawMessage
				json.Unmarshal(b, &resp)
				if string(resp["code"]) == "0" {
					t.Error("应该返回参数错误")
				}
			},
		},
		{
			name: "test: 时间格式验证",
			before: func() {
				// 不需要准备数据
			},
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				c.Request = httptest.NewRequest("GET", "/dashboard/metric/plan", nil)
				q := url.Values{}
				q.Add("team", "信贷团队")
				q.Add("business", "信贷稳定性1")
				q.Add("startMonth", "2025-13") // 错误的月份格式
				q.Add("endMonth", "2025-08")
				c.Request.URL.RawQuery = q.Encode()
				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]json.RawMessage
				json.Unmarshal(b, &resp)
				if string(resp["code"]) == "0" {
					t.Error("应该返回时间格式错误")
				}
			},
		},
		{
			name: "test: 正常查询",
			before: func() {
				// 不需要准备数据
			},
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				c.Request = httptest.NewRequest("GET", "/dashboard/metric/plan", nil)
				q := url.Values{}
				q.Add("team", "信贷团队")
				q.Add("business", "信贷稳定性1")
				q.Add("startMonth", "2025-08")
				q.Add("endMonth", "2025-08")
				c.Request.URL.RawQuery = q.Encode()
				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]json.RawMessage
				json.Unmarshal(b, &resp)
				// 正常情况下应该返回成功，即使没有数据
				if string(resp["code"]) != "0" {
					t.Error("正常查询应该返回成功")
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			GetPlanMetric(tt.args.c)
			if tt.expect != nil {
				tt.expect(t, tt.args.w)
			}
		})
	}
}





// TestOperationMetric_Structure 测试OperationMetric结构体
func TestOperationMetric_Structure(t *testing.T) {
	metric := OperationMetric{
		Total:       100,
		NotReported: 10,
		NotChecked:  5,
		NotStandard: 3,
	}

	assert.Equal(t, 100, metric.Total)
	assert.Equal(t, 10, metric.NotReported)
	assert.Equal(t, 5, metric.NotChecked)
	assert.Equal(t, 3, metric.NotStandard)
}

// TestCapacityMetric_Structure 测试CapacityMetric结构体
func TestCapacityMetric_Structure(t *testing.T) {
	metric := CapacityMetric{
		Coverage:     85.5,
		SuccessRate:  92.3,
		Efficiency:   78.9,
		AverageHours: 2.5,
	}

	assert.Equal(t, 85.5, metric.Coverage)
	assert.Equal(t, 92.3, metric.SuccessRate)
	assert.Equal(t, 78.9, metric.Efficiency)
	assert.Equal(t, 2.5, metric.AverageHours)
}

// TestDeploymentMetric_Structure 测试DeploymentMetric结构体
func TestDeploymentMetric_Structure(t *testing.T) {
	rollbackItems := []*RollbackItem{
		{Name: "app1", Value: 49.12},
		{Name: "app2", Value: 43.20},
	}

	metric := DeploymentMetric{
		FailureRate:  5.2,
		RollbackRate: 3.1,
		RollbackList: rollbackItems,
	}

	assert.Equal(t, 5.2, metric.FailureRate)
	assert.Equal(t, 3.1, metric.RollbackRate)
	assert.Len(t, metric.RollbackList, 2)
	assert.Equal(t, "app1", metric.RollbackList[0].Name)
	assert.Equal(t, 49.12, metric.RollbackList[0].Value)
}

// TestDeploymentMetric_RollbackListSorting 测试RollbackList排序功能
func TestDeploymentMetric_RollbackListSorting(t *testing.T) {
	// 创建测试数据，value 不按降序排列
	rollbackItems := []*RollbackItem{
		{Name: "app1", Value: 30.0},  // 最小值
		{Name: "app2", Value: 50.0},  // 最大值
		{Name: "app3", Value: 40.0},  // 中间值
	}

	metric := DeploymentMetric{
		FailureRate:  5.2,
		RollbackRate: 3.1,
		RollbackList: rollbackItems,
	}

	// 手动排序验证（模拟 GetDeploymentMetric 中的排序逻辑）
	sort.Slice(metric.RollbackList, func(i, j int) bool {
		return metric.RollbackList[i].Value > metric.RollbackList[j].Value
	})

	// 验证排序结果：应该按 value 降序排列
	assert.Len(t, metric.RollbackList, 3)
	assert.Equal(t, "app2", metric.RollbackList[0].Name) // 50.0 最大
	assert.Equal(t, 50.0, metric.RollbackList[0].Value)
	assert.Equal(t, "app3", metric.RollbackList[1].Name) // 40.0 中间
	assert.Equal(t, 40.0, metric.RollbackList[1].Value)
	assert.Equal(t, "app1", metric.RollbackList[2].Name) // 30.0 最小
	assert.Equal(t, 30.0, metric.RollbackList[2].Value)
}

// TestMonitorMetric_Structure 测试MonitorMetric结构体
func TestMonitorMetric_Structure(t *testing.T) {
	notRecalled := &NotRecalledInfo{P0: 2, P1: 5}

	metric := MonitorMetric{
		MachineCoverage:             95.0,
		BnsCoverage:                 88.5,
		CrossDepartmentBreachedRate: 2.3,
		CrossDepartmentTotal:        50,
		CrossDepartmentBuilt:        45,
		Recalled:                    20,
		NotRecalled:                 notRecalled,
	}

	assert.Equal(t, 95.0, metric.MachineCoverage)
	assert.Equal(t, 88.5, metric.BnsCoverage)
	assert.Equal(t, 2.3, metric.CrossDepartmentBreachedRate)
	assert.Equal(t, 50, metric.CrossDepartmentTotal)
	assert.Equal(t, 45, metric.CrossDepartmentBuilt)
	assert.Equal(t, 20, metric.Recalled)
	assert.NotNil(t, metric.NotRecalled)
	assert.Equal(t, 2, metric.NotRecalled.P0)
	assert.Equal(t, 5, metric.NotRecalled.P1)
}

// TestPlanMetric_Structure 测试PlanMetric结构体
func TestPlanMetric_Structure(t *testing.T) {
	emptyItems := []*EmptyItem{
		{ProductLine: "loan-fscene", Value: 49.12},
		{ProductLine: "loan-risk", Value: 43.20},
	}

	metric := PlanMetric{
		Coverage:    90.0,
		FailureRate: 1.5,
		EmptyRate:   5.0,
		EmptyList:   emptyItems,
	}

	assert.Equal(t, 90.0, metric.Coverage)
	assert.Equal(t, 1.5, metric.FailureRate)
	assert.Equal(t, 5.0, metric.EmptyRate)
	assert.Len(t, metric.EmptyList, 2)
	assert.Equal(t, "loan-fscene", metric.EmptyList[0].ProductLine)
	assert.Equal(t, 49.12, metric.EmptyList[0].Value)
}

// TestPlanMetric_EmptyListSorting 测试EmptyList排序功能
func TestPlanMetric_EmptyListSorting(t *testing.T) {
	// 创建测试数据，value 不按降序排列
	emptyItems := []*EmptyItem{
		{ProductLine: "loan-fscene", Value: 30.0},  // 最小值
		{ProductLine: "loan-risk", Value: 50.0},    // 最大值
		{ProductLine: "loan-credit", Value: 40.0},  // 中间值
	}

	metric := PlanMetric{
		Coverage:    90.0,
		FailureRate: 1.5,
		EmptyRate:   5.0,
		EmptyList:   emptyItems,
	}

	// 手动排序验证（模拟 GetPlanMetric 中的排序逻辑）
	sort.Slice(metric.EmptyList, func(i, j int) bool {
		return metric.EmptyList[i].Value > metric.EmptyList[j].Value
	})

	// 验证排序结果：应该按 value 降序排列
	assert.Len(t, metric.EmptyList, 3)
	assert.Equal(t, "loan-risk", metric.EmptyList[0].ProductLine)   // 50.0 最大
	assert.Equal(t, 50.0, metric.EmptyList[0].Value)
	assert.Equal(t, "loan-credit", metric.EmptyList[1].ProductLine) // 40.0 中间
	assert.Equal(t, 40.0, metric.EmptyList[1].Value)
	assert.Equal(t, "loan-fscene", metric.EmptyList[2].ProductLine) // 30.0 最小
	assert.Equal(t, 30.0, metric.EmptyList[2].Value)
}



// TestMetricParams_Structure 测试MetricParams结构体
func TestMetricParams_Structure(t *testing.T) {
	params := MetricParams{
		Team:       "信贷团队",
		Business:   "信贷稳定性1",
		StartMonth: "2025-08",
		EndMonth:   "2025-08",
	}

	assert.Equal(t, "信贷团队", params.Team)
	assert.Equal(t, "信贷稳定性1", params.Business)
	assert.Equal(t, "2025-08", params.StartMonth)
	assert.Equal(t, "2025-08", params.EndMonth)
	assert.True(t, params.StartTime.IsZero()) // 初始时间应该为零值
	assert.True(t, params.EndTime.IsZero())   // 初始时间应该为零值
}

// TestMetricParams_ShouldBindQuery 测试参数绑定功能
func TestMetricParams_ShouldBindQuery(t *testing.T) {
	tests := []struct {
		name        string
		queryParams string
		expectError bool
		errorMsg    string
		expected    MetricParams
	}{
		{
			name:        "正常参数绑定",
			queryParams: "team=信贷团队&business=信贷稳定性1&startMonth=2025-08&endMonth=2025-08",
			expectError: false,
			expected: MetricParams{
				Team:       "信贷团队",
				Business:   "信贷稳定性1",
				StartMonth: "2025-08",
				EndMonth:   "2025-08",
			},
		},
		{
			name:        "缺少必填参数startMonth",
			queryParams: "team=信贷团队&business=信贷稳定性1&endMonth=2025-08",
			expectError: true,
			errorMsg:    "StartMonth",
		},
		{
			name:        "缺少必填参数endMonth",
			queryParams: "team=信贷团队&business=信贷稳定性1&startMonth=2025-08",
			expectError: true,
			errorMsg:    "EndMonth",
		},
		{
			name:        "可选参数为空",
			queryParams: "startMonth=2025-08&endMonth=2025-08",
			expectError: false,
			expected: MetricParams{
				Team:       "",
				Business:   "",
				StartMonth: "2025-08",
				EndMonth:   "2025-08",
			},
		},
		{
			name:        "team和business为all",
			queryParams: "team=all&business=all&startMonth=2025-08&endMonth=2025-08",
			expectError: false,
			expected: MetricParams{
				Team:       "all",
				Business:   "all",
				StartMonth: "2025-08",
				EndMonth:   "2025-08",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建测试请求
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Request = httptest.NewRequest("GET", "/test?"+tt.queryParams, nil)

			// 绑定参数
			var params MetricParams
			err := c.ShouldBindQuery(&params)

			if tt.expectError {
				assert.Error(t, err)
				if tt.errorMsg != "" {
					assert.Contains(t, err.Error(), tt.errorMsg)
				}
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expected.Team, params.Team)
				assert.Equal(t, tt.expected.Business, params.Business)
				assert.Equal(t, tt.expected.StartMonth, params.StartMonth)
				assert.Equal(t, tt.expected.EndMonth, params.EndMonth)
			}
		})
	}
}

// TestParseMetricParams 测试解析指标参数函数
func TestParseMetricParams(t *testing.T) {
	type args struct {
		params *MetricParams
	}
	tests := []struct {
		name   string
		args   args
		expect func(*testing.T, error)
	}{
		{
			name: "test: 有效的时间格式",
			args: args{
				params: &MetricParams{
					Team:       "信贷团队",
					Business:   "信贷稳定性1",
					StartMonth: "2025-08",
					EndMonth:   "2025-08",
				},
			},
			expect: func(t *testing.T, err error) {
				if err != nil {
					t.Error("应该解析成功，但返回了错误:", err)
					return
				}
			},
		},
		{
			name: "test: 无效的开始月份格式",
			args: args{
				params: &MetricParams{
					StartMonth: "2025-13", // 无效月份
					EndMonth:   "2025-08",
				},
			},
			expect: func(t *testing.T, err error) {
				if err == nil {
					t.Error("应该返回错误，但解析成功了")
					return
				}
			},
		},
		{
			name: "test: 月份格式不完整",
			args: args{
				params: &MetricParams{
					StartMonth: "2025-8", // 缺少前导零
					EndMonth:   "2025-08",
				},
			},
			expect: func(t *testing.T, err error) {
				if err == nil {
					t.Error("应该返回错误，但解析成功了")
					return
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := parseMetricParams(tt.args.params)
			if tt.expect != nil {
				tt.expect(t, err)
			}
		})
	}
}

// TestParseMetricParams_Original 原有的详细测试
func TestParseMetricParams_Original(t *testing.T) {
	tests := []struct {
		name        string
		params      MetricParams
		expectError bool
		errorMsg    string
	}{
		{
			name: "有效的时间格式",
			params: MetricParams{
				Team:       "信贷团队",
				Business:   "信贷稳定性1",
				StartMonth: "2025-08",
				EndMonth:   "2025-08",
			},
			expectError: false,
		},
		{
			name: "无效的开始月份格式",
			params: MetricParams{
				Team:       "信贷团队",
				Business:   "信贷稳定性1",
				StartMonth: "2025-13", // 无效月份
				EndMonth:   "2025-08",
			},
			expectError: true,
			errorMsg:    "startMonth时间格式错误",
		},
		{
			name: "无效的结束月份格式",
			params: MetricParams{
				Team:       "信贷团队",
				Business:   "信贷稳定性1",
				StartMonth: "2025-08",
				EndMonth:   "25-08", // 年份格式错误
			},
			expectError: true,
			errorMsg:    "startMonth、endMonth必须是YYYY-MM格式",
		},
		{
			name: "月份格式不完整",
			params: MetricParams{
				Team:       "信贷团队",
				Business:   "信贷稳定性1",
				StartMonth: "2025-8", // 缺少前导零
				EndMonth:   "2025-08",
			},
			expectError: true,
			errorMsg:    "startMonth、endMonth必须是YYYY-MM格式",
		},
		{
			name: "使用错误的分隔符",
			params: MetricParams{
				Team:       "信贷团队",
				Business:   "信贷稳定性1",
				StartMonth: "2025/08", // 使用斜杠而不是横杠
				EndMonth:   "2025-08",
			},
			expectError: true,
			errorMsg:    "startMonth、endMonth必须是YYYY-MM格式",
		},
		{
			name: "跨年查询",
			params: MetricParams{
				Team:       "信贷团队",
				Business:   "信贷稳定性1",
				StartMonth: "2024-12",
				EndMonth:   "2025-01",
			},
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := parseMetricParams(&tt.params)

			if tt.expectError {
				assert.Error(t, err)
				if tt.errorMsg != "" {
					assert.Contains(t, err.Error(), tt.errorMsg)
				}
			} else {
				assert.NoError(t, err)
				// 验证时间字段已正确设置
				assert.False(t, tt.params.StartTime.IsZero())
				assert.False(t, tt.params.EndTime.IsZero())
				// 验证时区为 +08:00
				_, offset := tt.params.StartTime.Zone()
				assert.Equal(t, 8*3600, offset) // +08:00 = 8小时 = 28800秒
			}
		})
	}
}

// TestBuildMetricQuery 测试构建指标查询函数
func TestBuildMetricQuery(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")

	type args struct {
		metricName string
		params     *MetricParams
	}
	tests := []struct {
		name   string
		before func()
		args   args
		expect func(*testing.T, interface{}, error)
	}{
		{
			name: "test: 正常构建operation查询",
			before: func() {
				// 不需要准备数据
			},
			args: args{
				metricName: "operation",
				params: &MetricParams{
					Team:       "信贷团队",
					Business:   "信贷稳定性1",
					StartMonth: "2025-08",
					EndMonth:   "2025-08",
				},
			},
			expect: func(t *testing.T, query interface{}, err error) {
				if err != nil {
					t.Error("构建查询应该成功，但返回了错误:", err)
					return
				}
				if query == nil {
					t.Error("查询对象不应该为空")
					return
				}
			},
		},
		{
			name: "test: 正常构建capacity查询",
			before: func() {
				// 不需要准备数据
			},
			args: args{
				metricName: "capacity",
				params: &MetricParams{
					Team:       "信贷团队",
					Business:   "信贷稳定性1",
					StartMonth: "2025-08",
					EndMonth:   "2025-08",
				},
			},
			expect: func(t *testing.T, query interface{}, err error) {
				if err != nil {
					t.Error("构建查询应该成功，但返回了错误:", err)
					return
				}
				if query == nil {
					t.Error("查询对象不应该为空")
					return
				}
			},
		},
		{
			name: "test: team和business为all",
			before: func() {
				// 不需要准备数据
			},
			args: args{
				metricName: "operation",
				params: &MetricParams{
					Team:       "all",
					Business:   "all",
					StartMonth: "2025-08",
					EndMonth:   "2025-09",
				},
			},
			expect: func(t *testing.T, query interface{}, err error) {
				if err != nil {
					t.Error("构建查询应该成功，但返回了错误:", err)
					return
				}
				if query == nil {
					t.Error("查询对象不应该为空")
					return
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			// 先解析时间参数
			parseMetricParams(tt.args.params)
			query, err := buildMetricQuery(tt.args.metricName, tt.args.params)
			if tt.expect != nil {
				tt.expect(t, query, err)
			}
		})
	}
}



// TestParseMetricParams_EdgeCases 测试边界条件
func TestParseMetricParams_EdgeCases(t *testing.T) {
	tests := []struct {
		name        string
		params      MetricParams
		expectError bool
		errorMsg    string
	}{
		{
			name: "月份为00",
			params: MetricParams{
				StartMonth: "2025-00",
				EndMonth:   "2025-08",
			},
			expectError: true,
			errorMsg:    "startMonth时间格式错误",
		},
		{
			name: "月份为13",
			params: MetricParams{
				StartMonth: "2025-08",
				EndMonth:   "2025-13",
			},
			expectError: true,
			errorMsg:    "endMonth时间格式错误",
		},
		{
			name: "年份为3位数",
			params: MetricParams{
				StartMonth: "202-08",
				EndMonth:   "2025-08",
			},
			expectError: true,
			errorMsg:    "startMonth、endMonth必须是YYYY-MM格式",
		},
		{
			name: "年份为5位数",
			params: MetricParams{
				StartMonth: "20255-08",
				EndMonth:   "2025-08",
			},
			expectError: true,
			errorMsg:    "startMonth、endMonth必须是YYYY-MM格式",
		},
		{
			name: "包含特殊字符",
			params: MetricParams{
				StartMonth: "2025-0a",
				EndMonth:   "2025-08",
			},
			expectError: true,
			errorMsg:    "startMonth、endMonth必须是YYYY-MM格式",
		},
		{
			name: "使用点号分隔",
			params: MetricParams{
				StartMonth: "2025.08",
				EndMonth:   "2025-08",
			},
			expectError: true,
			errorMsg:    "startMonth、endMonth必须是YYYY-MM格式",
		},
		{
			name: "空格字符",
			params: MetricParams{
				StartMonth: "2025 08",
				EndMonth:   "2025-08",
			},
			expectError: true,
			errorMsg:    "startMonth、endMonth必须是YYYY-MM格式",
		},
		{
			name: "前后有空格",
			params: MetricParams{
				StartMonth: " 2025-08 ",
				EndMonth:   "2025-08",
			},
			expectError: true,
			errorMsg:    "startMonth、endMonth必须是YYYY-MM格式",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := parseMetricParams(&tt.params)

			if tt.expectError {
				assert.Error(t, err)
				if tt.errorMsg != "" {
					assert.Contains(t, err.Error(), tt.errorMsg)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// TestParseMetricParams_TimeZone 测试时区处理
func TestParseMetricParams_TimeZone(t *testing.T) {
	params := MetricParams{
		StartMonth: "2025-08",
		EndMonth:   "2025-08",
	}

	err := parseMetricParams(&params)
	assert.NoError(t, err)

	// 验证时区为 Asia/Shanghai (+08:00)
	_, offset := params.StartTime.Zone()
	assert.Equal(t, 8*3600, offset) // +08:00 = 8小时 = 28800秒

	_, offset = params.EndTime.Zone()
	assert.Equal(t, 8*3600, offset)

	// 验证时间为月初
	assert.Equal(t, 1, params.StartTime.Day())
	assert.Equal(t, 1, params.EndTime.Day())
	assert.Equal(t, 0, params.StartTime.Hour())
	assert.Equal(t, 0, params.EndTime.Hour())
	assert.Equal(t, 0, params.StartTime.Minute())
	assert.Equal(t, 0, params.EndTime.Minute())
	assert.Equal(t, 0, params.StartTime.Second())
	assert.Equal(t, 0, params.EndTime.Second())
}

// TestBuildMetricQuery_ErrorHandling 测试查询构建错误处理
func TestBuildMetricQuery_ErrorHandling(t *testing.T) {
	// 不初始化环境，测试数据库连接错误
	params := &MetricParams{
		Team:       "信贷团队",
		Business:   "信贷稳定性1",
		StartMonth: "2025-08",
		EndMonth:   "2025-08",
	}

	// 解析时间参数
	err := parseMetricParams(params)
	assert.NoError(t, err)

	// 测试数据库连接错误（没有初始化环境）
	query, err := buildMetricQuery("operation", params)
	// 在没有数据库连接的情况下应该返回错误
	if err != nil {
		assert.Error(t, err)
		assert.Nil(t, query)
	}
}

// BenchmarkParseMetricParams 性能测试：参数解析
func BenchmarkParseMetricParams(b *testing.B) {
	params := MetricParams{
		StartMonth: "2025-08",
		EndMonth:   "2025-08",
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		parseMetricParams(&params)
	}
}

// TestMetricFunctions_Integration 集成测试：测试完整的请求流程
func TestMetricFunctions_Integration(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")

	tests := []struct {
		name     string
		function func(*gin.Context)
		url      string
	}{
		{
			name:     "GetOperationMetric集成测试",
			function: GetOperationMetric,
			url:      "/operation?team=信贷团队&business=信贷稳定性1&startMonth=2025-08&endMonth=2025-08",
		},
		{
			name:     "GetCapacityMetric集成测试",
			function: GetCapacityMetric,
			url:      "/capacity?team=信贷团队&business=信贷稳定性1&startMonth=2025-08&endMonth=2025-08",
		},
		{
			name:     "GetDeploymentMetric集成测试",
			function: GetDeploymentMetric,
			url:      "/deployment?team=信贷团队&business=信贷稳定性1&startMonth=2025-08&endMonth=2025-08",
		},
		{
			name:     "GetMonitorMetric集成测试",
			function: GetMonitorMetric,
			url:      "/monitor?team=信贷团队&business=信贷稳定性1&startMonth=2025-08&endMonth=2025-08",
		},
		{
			name:     "GetPlanMetric集成测试",
			function: GetPlanMetric,
			url:      "/plan?team=信贷团队&business=信贷稳定性1&startMonth=2025-08&endMonth=2025-08",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Request = httptest.NewRequest("GET", tt.url, nil)

			// 执行函数
			tt.function(c)

			// 验证响应
			assert.Equal(t, 200, w.Code)

			// 验证响应体是有效的JSON
			var response map[string]interface{}
			err := json.Unmarshal(w.Body.Bytes(), &response)
			assert.NoError(t, err)

			// 验证响应结构
			assert.Contains(t, response, "code")
			assert.Contains(t, response, "data")
		})
	}
}



// BenchmarkStandardParseInt 性能测试：标准整数转换（对比）
func BenchmarkStandardParseInt(b *testing.B) {
	for i := 0; i < b.N; i++ {
		strconv.Atoi("12345")
	}
}
