package stability

import (
	"bytes"
	"context"
	"encoding/json"
	"io"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"

	"stabilityDigitalBase/env"
	"stabilityDigitalBase/library/ent/metric"
	"stabilityDigitalBase/library/mysql"
)

// TestImportMetrics 测试导入指标数据接口
func TestImportMetrics(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")

	type args struct {
		c *gin.Context
		w *httptest.ResponseRecorder
	}
	tests := []struct {
		name   string
		before func()
		args   args
		expect func(*testing.T, *httptest.ResponseRecorder)
	}{
		{
			name: "test: 正常导入指标数据",
			before: func() {
				db, _ := mysql.Database()
				// 清理数据
				db.Metric.Delete().ExecX(context.Background())
			},
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)

				// 构建测试数据
				testData := Metrics{
					Team:     "信贷团队",
					Business: "信贷稳定性1",
					Month:    "2025-08",
					Operation: &OperationMetric{
						Total:       100,
						NotReported: 10,
						NotChecked:  5,
						NotStandard: 3,
					},
					Capacity: &CapacityMetric{
						Coverage:     85.5,
						SuccessRate:  92.3,
						Efficiency:   78.9,
						AverageHours: 2.5,
					},
					Deployment: &DeploymentMetric{
						FailureRate:  5.2,
						RollbackRate: 3.1,
						RollbackList: []*RollbackItem{
							{Name: "test_app", Value: 49.12},
						},
					},
					Monitor: &MonitorMetric{
						MachineCoverage:             95.0,
						BnsCoverage:                 88.5,
						CrossDepartmentBreachedRate: 2.3,
						CrossDepartmentTotal:        50,
						CrossDepartmentBuilt:        45,
						Recalled:                    20,
						NotRecalled: &NotRecalledInfo{
							P0: 2,
							P1: 3,
						},
					},
					Plan: &PlanMetric{
						Coverage:    90.0,
						FailureRate: 1.5,
						EmptyRate:   5.0,
						EmptyList: []*EmptyItem{
							{ProductLine: "loan-fscene", Value: 49.12},
						},
					},
				}

				jsonData, _ := json.Marshal(testData)
				c.Request = httptest.NewRequest("POST", "/dashboard/metrics/import", bytes.NewBuffer(jsonData))
				c.Request.Header.Set("Content-Type", "application/json")
				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]json.RawMessage
				json.Unmarshal(b, &resp)
				if string(resp["code"]) != "0" {
					t.Error(w.Code, string(b))
					return
				}

				// 验证数据库中的数据
				db, _ := mysql.Database()
				metrics, err := db.Metric.Query().
					Where(
						metric.TeamEQ("信贷团队"),
						metric.BusinessEQ("信贷稳定性1"),
						metric.MonthEQ(time.Date(2025, 8, 01, 0, 0, 0, 0, time.Local)),
					).
					All(context.Background())

				if err != nil {
					t.Error("查询数据库失败:", err)
					return
				}

				if len(metrics) != 5 {
					t.Error("指标数量错误，期望5个，实际:", len(metrics))
					return
				}
			},
		},
		{
			name: "test: 参数验证 - 无效JSON",
			before: func() {
				// 不需要准备数据
			},
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)

				// 发送无效的JSON数据
				c.Request = httptest.NewRequest("POST", "/dashboard/metrics/import", bytes.NewBufferString("invalid json"))
				c.Request.Header.Set("Content-Type", "application/json")
				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]json.RawMessage
				json.Unmarshal(b, &resp)
				if string(resp["code"]) == "0" {
					t.Error("应该返回参数错误")
				}
			},
		},
		{
			name: "test: 参数验证 - 缺少必要字段",
			before: func() {
				// 不需要准备数据
			},
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)

				// 构建缺少必要字段的数据
				testData := map[string]any{
					"team": "信贷团队",
					// 缺少 business 和 month 字段
				}

				jsonData, _ := json.Marshal(testData)
				c.Request = httptest.NewRequest("POST", "/dashboard/metrics/import", bytes.NewBuffer(jsonData))
				c.Request.Header.Set("Content-Type", "application/json")
				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]json.RawMessage
				json.Unmarshal(b, &resp)
				if string(resp["code"]) == "0" {
					t.Error("应该返回参数错误")
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			ImportMetrics(tt.args.c)
			if tt.expect != nil {
				tt.expect(t, tt.args.w)
			}
		})
	}
}
