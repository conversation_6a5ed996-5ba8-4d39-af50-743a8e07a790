package stability

import (
	"encoding/json"
	"regexp"
	"sort"
	"time"

	"github.com/gin-gonic/gin"

	"dt-common/gintool"
	"stabilityDigitalBase/library/ent"
	"stabilityDigitalBase/library/ent/metric"
	"stabilityDigitalBase/library/errs"
	"stabilityDigitalBase/library/mysql"
)

// MetricParams 指标查询参数结构体
type MetricParams struct {
	Team       string    `form:"team"`
	Business   string    `form:"business"`
	StartMonth string    `form:"startMonth" binding:"required"`
	EndMonth   string    `form:"endMonth" binding:"required"`
	StartTime  time.Time `form:"-"`
	EndTime    time.Time `form:"-"`
}

// parseMetricParams 解析指标查询参数的时间字段
func parseMetricParams(params *MetricParams) error {
	// 验证时间格式（YYYY-MM）
	startMatched, _ := regexp.MatchString(`^\d{4}-\d{2}$`, params.StartMonth)
	endMatched, _ := regexp.MatchString(`^\d{4}-\d{2}$`, params.EndMonth)
	if !startMatched || !endMatched {
		return errs.CodeRequestParameter.Detail("startMonth、endMonth必须是YYYY-MM格式")
	}

	// 转换时间格式（YYYY-MM -> YYYY-MM-01 00:00:00 +08:00）
	loc, _ := time.LoadLocation("Asia/Shanghai")
	startTime, err := time.ParseInLocation("2006-01-02", params.StartMonth+"-01", loc)
	if err != nil {
		return errs.CodeRequestParameter.Detail("startMonth时间格式错误")
	}
	endTime, err := time.ParseInLocation("2006-01-02", params.EndMonth+"-01", loc)
	if err != nil {
		return errs.CodeRequestParameter.Detail("endMonth时间格式错误")
	}

	params.StartTime = startTime
	params.EndTime = endTime
	return nil
}

// buildMetricQuery 构建指标查询
func buildMetricQuery(metricName string, params *MetricParams) (*ent.MetricQuery, error) {
	db, err := mysql.Database()
	if err != nil {
		return nil, err
	}

	// 构建查询条件
	query := db.Metric.Query().Where(metric.NameEQ(metricName))

	// 添加团队条件
	if params.Team != "" && params.Team != "all" {
		query = query.Where(metric.TeamEQ(params.Team))
	}

	// 添加业务条件
	if params.Business != "" && params.Business != "all" {
		query = query.Where(metric.BusinessEQ(params.Business))
	}

	// 添加时间范围条件
	if params.StartMonth == params.EndMonth {
		query = query.Where(metric.MonthEQ(params.StartTime))
	} else {
		query = query.Where(metric.MonthGTE(params.StartTime), metric.MonthLTE(params.EndTime))
	}

	return query, nil
}

// GetOperationMetric 获取操作风险类指标
func GetOperationMetric(c *gin.Context) {
	// 绑定查询参数
	var params MetricParams
	if err := c.ShouldBindQuery(&params); err != nil {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}

	// 解析时间参数
	if err := parseMetricParams(&params); err != nil {
		gintool.JSON2FE(c, nil, err)
		return
	}

	// 构建查询
	query, err := buildMetricQuery("operation", &params)
	if err != nil {
		gintool.JSON2FE(c, nil, err)
		return
	}

	// 查询操作风险类指标
	ctx, cancel := mysql.ContextWithTimeout()
	metricRecords, err := query.All(ctx)
	cancel()
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}

	// 聚合数据（求和）
	result := OperationMetric{}
	for _, record := range metricRecords {
		var metric OperationMetric
		if err := json.Unmarshal([]byte(record.Value), &metric); err != nil {
			continue // 跳过解析失败的记录
		}
		result.Total += metric.Total
		result.NotReported += metric.NotReported
		result.NotChecked += metric.NotChecked
		result.NotStandard += metric.NotStandard
	}

	gintool.JSON2FE(c, result, nil)
}

// // CapacityMetric 容量类指标结构
// type CapacityMetric struct {
// 	Coverage     float64 `json:"coverage"`
// 	SuccessRate  float64 `json:"successRate"`
// 	Efficiency   float64 `json:"efficiency"`
// 	AverageHours float64 `json:"averageHours"`
// }

// GetCapacityMetric 获取容量类指标
func GetCapacityMetric(c *gin.Context) {
	// 绑定查询参数
	var params MetricParams
	if err := c.ShouldBindQuery(&params); err != nil {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}

	// 解析时间参数
	if err := parseMetricParams(&params); err != nil {
		gintool.JSON2FE(c, nil, err)
		return
	}

	// 构建查询
	query, err := buildMetricQuery("capacity", &params)
	if err != nil {
		gintool.JSON2FE(c, nil, err)
		return
	}

	// 查询容量类指标
	ctx, cancel := mysql.ContextWithTimeout()
	metricRecords, err := query.All(ctx)
	cancel()
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}

	// 聚合数据（容量类指标需要计算平均值）
	result := CapacityMetric{}
	count := len(metricRecords)
	if count == 0 {
		gintool.JSON2FE(c, result, nil)
		return
	}

	for _, record := range metricRecords {
		var metric CapacityMetric
		if err := json.Unmarshal([]byte(record.Value), &metric); err != nil {
			continue // 跳过解析失败的记录
		}
		result.Coverage += metric.Coverage
		result.SuccessRate += metric.SuccessRate
		result.Efficiency += metric.Efficiency
		result.AverageHours += metric.AverageHours
	}

	// 计算平均值
	result.Coverage /= float64(count)
	result.SuccessRate /= float64(count)
	result.Efficiency /= float64(count)
	result.AverageHours /= float64(count)

	gintool.JSON2FE(c, result, nil)
}

// GetDeploymentMetric 获取部署类指标
func GetDeploymentMetric(c *gin.Context) {
	// 绑定查询参数
	var params MetricParams
	if err := c.ShouldBindQuery(&params); err != nil {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}

	// 解析时间参数
	if err := parseMetricParams(&params); err != nil {
		gintool.JSON2FE(c, nil, err)
		return
	}

	// 构建查询
	query, err := buildMetricQuery("deployment", &params)
	if err != nil {
		gintool.JSON2FE(c, nil, err)
		return
	}

	// 查询部署类指标
	ctx, cancel := mysql.ContextWithTimeout()
	metricRecords, err := query.All(ctx)
	cancel()
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}

	// 聚合数据
	result := DeploymentMetric{RollbackList: []*RollbackItem{}}
	count := len(metricRecords)
	if count == 0 {
		gintool.JSON2FE(c, result, nil)
		return
	}

	rollbackMap := make(map[string]float64) // 用于聚合回滚列表
	rollbackCount := make(map[string]int)   // 用于记录每个name出现的次数

	for _, record := range metricRecords {
		var metric DeploymentMetric
		if err := json.Unmarshal([]byte(record.Value), &metric); err != nil {
			continue // 跳过解析失败的记录
		}
		result.FailureRate += metric.FailureRate
		result.RollbackRate += metric.RollbackRate

		// 聚合回滚列表（按name合并，记录出现次数）
		for _, item := range metric.RollbackList {
			rollbackMap[item.Name] += item.Value
			rollbackCount[item.Name]++
		}
	}

	// 计算平均值
	result.FailureRate /= float64(count)
	result.RollbackRate /= float64(count)

	// 构建聚合后的回滚列表（按name求平均）
	for name, totalValue := range rollbackMap {
		avgValue := totalValue / float64(rollbackCount[name])
		result.RollbackList = append(result.RollbackList, &RollbackItem{
			Name:  name,
			Value: avgValue,
		})
	}

	// 按 value 倒排（降序排列）
	sort.Slice(result.RollbackList, func(i, j int) bool {
		return result.RollbackList[i].Value > result.RollbackList[j].Value
	})

	gintool.JSON2FE(c, result, nil)
}

// GetMonitorMetric 获取监控类指标
func GetMonitorMetric(c *gin.Context) {
	// 绑定查询参数
	var params MetricParams
	if err := c.ShouldBindQuery(&params); err != nil {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}

	// 解析时间参数
	if err := parseMetricParams(&params); err != nil {
		gintool.JSON2FE(c, nil, err)
		return
	}

	// 构建查询
	query, err := buildMetricQuery("monitor", &params)
	if err != nil {
		gintool.JSON2FE(c, nil, err)
		return
	}

	// 查询监控类指标
	ctx, cancel := mysql.ContextWithTimeout()
	metricRecords, err := query.All(ctx)
	cancel()
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}

	// 聚合数据
	result := MonitorMetric{NotRecalled: &NotRecalledInfo{}}
	count := len(metricRecords)
	if count == 0 {
		gintool.JSON2FE(c, result, nil)
		return
	}

	for _, record := range metricRecords {
		var metric MonitorMetric
		if err := json.Unmarshal([]byte(record.Value), &metric); err != nil {
			continue // 跳过解析失败的记录
		}
		result.MachineCoverage += metric.MachineCoverage
		result.BnsCoverage += metric.BnsCoverage
		result.CrossDepartmentBreachedRate += metric.CrossDepartmentBreachedRate
		result.CrossDepartmentTotal += metric.CrossDepartmentTotal
		result.CrossDepartmentBuilt += metric.CrossDepartmentBuilt
		result.Recalled += metric.Recalled

		if metric.NotRecalled != nil {
			result.NotRecalled.P0 += metric.NotRecalled.P0
			result.NotRecalled.P1 += metric.NotRecalled.P1
		}
	}

	// 计算平均值（覆盖率类指标）和总和（数量类指标）
	result.MachineCoverage /= float64(count)
	result.BnsCoverage /= float64(count)
	result.CrossDepartmentBreachedRate /= float64(count)
	// CrossDepartmentTotal、CrossDepartmentBuilt、Recalled、NotRecalled 保持总和

	gintool.JSON2FE(c, result, nil)
}

// GetPlanMetric 获取预案平台类指标
func GetPlanMetric(c *gin.Context) {
	// 绑定查询参数
	var params MetricParams
	if err := c.ShouldBindQuery(&params); err != nil {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}

	// 解析时间参数
	if err := parseMetricParams(&params); err != nil {
		gintool.JSON2FE(c, nil, err)
		return
	}

	// 构建查询
	query, err := buildMetricQuery("plan", &params)
	if err != nil {
		gintool.JSON2FE(c, nil, err)
		return
	}

	// 查询预案平台类指标
	ctx, cancel := mysql.ContextWithTimeout()
	metricRecords, err := query.All(ctx)
	cancel()
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}

	// 聚合数据
	result := PlanMetric{EmptyList: []*EmptyItem{}}
	count := len(metricRecords)
	if count == 0 {
		gintool.JSON2FE(c, result, nil)
		return
	}

	emptyMap := make(map[string]float64) // 用于聚合切空列表
	emptyCount := make(map[string]int)   // 用于记录每个productLine出现的次数

	for _, record := range metricRecords {
		var metric PlanMetric
		if err := json.Unmarshal([]byte(record.Value), &metric); err != nil {
			continue // 跳过解析失败的记录
		}
		result.Coverage += metric.Coverage
		result.FailureRate += metric.FailureRate
		result.EmptyRate += metric.EmptyRate

		// 聚合切空列表（按productLine合并，记录出现次数）
		for _, item := range metric.EmptyList {
			emptyMap[item.ProductLine] += item.Value
			emptyCount[item.ProductLine]++
		}
	}

	// 计算平均值
	result.Coverage /= float64(count)
	result.FailureRate /= float64(count)
	result.EmptyRate /= float64(count)

	// 构建聚合后的切空列表（按productLine求平均）
	for productLine, totalValue := range emptyMap {
		avgValue := totalValue / float64(emptyCount[productLine])
		result.EmptyList = append(result.EmptyList, &EmptyItem{
			ProductLine: productLine,
			Value:       avgValue,
		})
	}

	// 按 value 倒排（降序排列）
	sort.Slice(result.EmptyList, func(i, j int) bool {
		return result.EmptyList[i].Value > result.EmptyList[j].Value
	})

	gintool.JSON2FE(c, result, nil)
}
