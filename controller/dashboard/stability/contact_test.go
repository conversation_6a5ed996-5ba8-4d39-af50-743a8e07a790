package stability

import (
	"context"
	"encoding/json"
	"io"
	"net/http/httptest"
	"net/url"
	"testing"
	"time"

	"github.com/gin-gonic/gin"

	"stabilityDigitalBase/env"
	"stabilityDigitalBase/library/mysql"
)

// TestGetContact 测试获取接口人信息接口
func TestGetContact(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")

	type args struct {
		c *gin.Context
		w *httptest.ResponseRecorder
	}
	tests := []struct {
		name   string
		before func()
		args   args
		expect func(*testing.T, *httptest.ResponseRecorder)
	}{
		{
			name: "test: 正常查询接口人信息",
			before: func() {
				db, _ := mysql.Database()
				// 清理数据
				db.Contact.Delete().ExecX(context.Background())

				// 创建测试数据
				db.Contact.Create().SetTeam("信贷团队").SetBusiness("信贷稳定性1").SetAccount("zhaopinglan_dxm").SetName("赵平兰").SetStartTime(time.Date(2025, 1, 1, 0, 0, 0, 0, time.Local)).ExecX(context.Background())
			},
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				c.Request = httptest.NewRequest("GET", "/dashboard/contact", nil)
				q := url.Values{}
				q.Add("team", "信贷团队")
				q.Add("business", "信贷稳定性1")
				q.Add("endMonth", "2025-08")
				c.Request.URL.RawQuery = q.Encode()
				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]json.RawMessage
				json.Unmarshal(b, &resp)
				if string(resp["code"]) != "0" {
					t.Error(w.Code, string(b))
					return
				}

				var result ContactResponse
				json.Unmarshal(resp["data"], &result)
				if len(result.History) == 0 {
					t.Error("接口人历史记录为空")
					return
				}
			},
		},
		{
			name: "test: 参数验证",
			before: func() {
				// 不需要准备数据
			},
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				c.Request = httptest.NewRequest("GET", "/dashboard/contact", nil)
				q := url.Values{}
				q.Add("team", "信贷团队")
				q.Add("business", "信贷稳定性1")
				// 缺少 endMonth 参数
				c.Request.URL.RawQuery = q.Encode()
				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]json.RawMessage
				json.Unmarshal(b, &resp)
				if string(resp["code"]) == "0" {
					t.Error("应该返回参数错误")
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			GetContact(tt.args.c)
			if tt.expect != nil {
				tt.expect(t, tt.args.w)
			}
		})
	}
}
