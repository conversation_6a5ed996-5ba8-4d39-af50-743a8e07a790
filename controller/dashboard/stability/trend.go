package stability

import (
	"github.com/gin-gonic/gin"

	"dt-common/gintool"
	"stabilityDigitalBase/library/errs"
	"stabilityDigitalBase/library/mysql"
)

// GetOperationTrendMetric 获取操作风险类指标趋势
func GetOperationTrendMetric(c *gin.Context) {
	// 绑定查询参数
	var params MetricParams
	if err := c.ShouldBindQuery(&params); err != nil {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}

	// 解析时间参数
	if err := parseMetricParams(&params); err != nil {
		gintool.JSON2FE(c, nil, err)
		return
	}

	// 构建查询
	query, err := buildMetricQuery("operation", &params)
	if err != nil {
		gintool.JSON2FE(c, nil, err)
		return
	}

	// 查询操作风险类指标
	ctx, cancel := mysql.ContextWithTimeout()
	metricRecords, err := query.All(ctx)
	cancel()
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}

	// 按月聚合数据（求和）
	

	// gintool.JSON2FE(c, result, nil)
}
