package router

import (
	"github.com/gin-gonic/gin"

	"stabilityDigitalBase/controller/dashboard/stability"
	"stabilityDigitalBase/controller/incident"
)

// Routes 数字化项目接口
func Routes(r *gin.Engine, token string) {
	// 项目管理路由
	apiGroup := r.Group("/api")
	{
		apiGroup.GET("/ping", func(c *gin.Context) {
			c.String(200, "pong")
		})
	}

	frontGroup := r.Group("/digital-sre")
	{
		// 故障管理路由
		incidentGroup := frontGroup.Group("/incident")
		{
			incidentGroup.POST("/create", incident.Create)             // 故障创建
			incidentGroup.POST("/modify", incident.Modify)             // 故障信息维护
			incidentGroup.POST("/updateStatus", incident.UpdateStatus) // 更新故障状态
			incidentGroup.POST("/uploadFile", incident.UploadFile)     // 附件上传
			incidentGroup.GET("/list", incident.List)                  // 故障列表查询
			incidentGroup.GET("/detail/:id", incident.Detail)          // 故障详情查询

			// 问题管理路由
			incidentGroup.POST("/problem/create", incident.CreateProblem) // 创建问题
			incidentGroup.POST("/problem/modify", incident.ModifyProblem) // 修改问题
			incidentGroup.DELETE("/problem/:id", incident.DeleteProblem)  // 删除问题
			incidentGroup.GET("/problems", incident.ListProblems)         // 查询故障的问题列表

			// 改进项管理路由
			incidentGroup.POST("/improvement/create", incident.CreateImprovement)             // 创建改进项
			incidentGroup.POST("/improvement/modify", incident.ModifyImprovement)             // 修改改进项
			incidentGroup.POST("/improvement/updateStatus", incident.UpdateImprovementStatus) // 更新改进项状态
			incidentGroup.DELETE("/improvement/:id", incident.DeleteImprovement)              // 删除改进项
			incidentGroup.GET("/improvements", incident.ListImprovements)                     // 查询问题的改进项列表
		}

		// 仪表盘路由
		dashboardGroup := frontGroup.Group("/dashboard")
		{
			dashboardGroup.POST("/contact/create", stability.CreateContact) // 新建接口人
			dashboardGroup.POST("/metric/import", stability.ImportMetrics)  // 导入指标数据
			dashboardGroup.POST("/ metric/update", stability.UpdateMetrics) // 更新指标数据

			// 接口人场景
			dashboardGroup.GET("/business", stability.GetBusiness)                       // 业务团队下拉列表
			dashboardGroup.GET("/contact", stability.GetContact)                         // 获取接口人信息
			dashboardGroup.GET("/operation/aggregation", stability.GetOperationMetric)     // 操作风险类指标
			dashboardGroup.GET("/operation/trend", stability.GetOperationTrendMetric)   // 操作风险类指标（趋势）
			dashboardGroup.GET("/capacity/aggregation", stability.GetCapacityMetric)     // 容量类指标
			dashboardGroup.GET("/capacity/trend", stability.GetCapacityMetric)           // 容量类指标（趋势）
			dashboardGroup.GET("/deployment/aggregation", stability.GetDeploymentMetric) // 部署类指标
			dashboardGroup.GET("/deployment/trend", stability.GetDeploymentMetric)       // 部署类指标（趋势）
			dashboardGroup.GET("/monitor/aggregation", stability.GetMonitorMetric)       // 监控类指标
			dashboardGroup.GET("/monitor/trend", stability.GetMonitorMetric)             // 监控类指标（趋势）
			dashboardGroup.GET("/plan/aggregation", stability.GetPlanMetric)             // 预案平台类指标
			dashboardGroup.GET("/plan/trend", stability.GetPlanMetric)                   // 预案平台类指标（趋势）

		}
	}
}
