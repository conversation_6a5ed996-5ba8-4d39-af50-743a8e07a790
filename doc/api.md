# API接口文档

## 0. 公共响应体

```json
{
  "code": 0,
  "message": "success", // code=0 时 message 为 success，code!=0 时 message 为错误信息
  "data": any
}
```

## 1. 故障管理

### 1.1 一句话故障创建

- **URL**: `/incident/create`
- **方法**: `POST`
- **请求体**:

```json
{
    "happenedAt": "2025-08-05",      // 必填
    "summary": "故障简述",            // 必填
    "directCause": "直接原因",        // 必填
    "nature": "good",                       // 选填
    "timeDiscovered": 1,                    // 选填
    "timeLocated": 10,                      // 选填
    "timeRecovered": 100,                   // 选填
    "autoStopLoss": true,                   // 选填
    "autoStopLossEffectived": false,        // 选填
    "attachments": [attchmentId]            // 选填
}
```

- **响应**:

```json
{
    "code": 0,
    "message": "",
    "data": {
        "incidentId": 1
    }
}   
```

### 1.2 故障信息维护

- **URL**: `/incident/modify`
- **方法**: `POST`
- **请求体**:

```json
{
    "id": 1,
    "happenedAt": "2025-08-05",      // 必填
    "summary": "故障简述",            // 必填
    "directCause": "直接原因",        // 必填
    "nature": "good",                       // 选填
    "timeDiscovered": 1,                    // 选填
    "timeLocated": 10,                      // 选填
    "timeRecovered": 100,                   // 选填
    "autoStopLoss": true,                   // 选填
    "autoStopLossEffectived": false,        // 选填
    "attachments": [attchmentId]            // 选填
}
```

- **响应**:

```json
{
    "code": 0,
    "message": "",
    "data": "ok"
}
```

### 1.3 创建附件

- **URL**: `/incident/attachment/create`
- **方法**: `POST`
- **说明**: file - 前端上传文件到bos得到url，调接口换attachmentId; text - 直接传content
- **请求体**:

```json
{
    "id": 1,
    "incidentId": 1,
    "type": "file",     // 必填，file or text
    "content": "",      // 文本类型选填
    "fileUrl": "",      // 文件类型选填
}
```

- **响应**:

```json
{
    "code": 0,
    "message": "",
    "data": {
        "attachmentId": 1
    }
}
```

### 1.4 删除附件

- **URL**: `/incident/attachment/delete`
- **方法**: `POST`

- **请求体**:

```json
{
    "id": 1,
}
```

- **响应**:

```json
{
    "code": 0,
    "message": "",
    "data": "ok"
}
```

### 1.5 创建时间线

- **URL**: `/incident/timeline/create`
- **方法**: `POST`
- **请求体**:

```json
{
    "id": 1,
    "incident_id": 1,
    "startTime": "2025-08-15 12:00:00",
    "endTime":"",      // 选填
    "type": "",        // 选填, occurred/discovered/located/operated/recovered
    "description": "发生了什么事情"
}
```

- **响应**:

```json
{
    "code": 0,
    "message": "",
    "data": {
        "timelineId": 1
    }
}
```

### 1.6 更新时间线

- **URL**: `/incident/timeline/modify`
- **方法**: `POST`
- **请求体**:

```json
{
    "id": 1,
    "incident_id": 1,
    "startTime": "2025-08-15 12:00:00",
    "endTime":"",      // 选填
    "type": "",        // 选填, occurred/discovered/located/operated/recovered
    "description": "发生了什么事情"
}
```

- **响应**:

```json
{
    "code": 0,
    "message": "",
    "data": "ok"
}
```

### 1.7 删除时间线

- **URL**: `/incident/timeline/delete`
- **方法**: `POST`
- **请求体**:

```json
{
    "id": 1
}
```

- **响应**:

```json
{
    "code": 0,
    "message": "",
    "data": "ok"
}
```

### 1.8 问题&改进项维护

- **URL**: `/incident/problem/create`
- **方法**: `POST`
- **请求体**:

```json
{
    "incidentId": 1,
    "phase": "deploy",
    "problem": "暴露的问题",                  
    "improvements": [{
        "content": "整改措施",
        "owner":"整改负责人",
        "expected_time": "2025-08-15",
        "status": "pending"
    }]
}
```

- **响应**:

```json
{
    "code": 0,
    "message": "",
    "data": {
        "problemId": 1,
        "phase": "deploy",
        "problem": "1、问题\n2、问题",
        "improvements": [{
            "id": 1,
            "content": "整改措施",
            "owner":"整改负责人",
            "expected_time": "2025-08-15",
            "status": "pending"
        }]
    }
}
```

### 1.9 复盘信息维护

- **URL**: `/incident/modify`
- **方法**: `POST`
- **请求体**:

```json
{
    "id": 1,
    "happenedAt": "2025-08-05",
    "summary": "故障简述",
    "time_discovered": 1,                   // 选填
    "time_located": 10,                     // 选填
    "time_recovered": 100,                  // 选填
    "owner": "负责人",
    "level": "P0",
    "directCause": "直接原因",
    "rootCause": "根本原因",
    "impact": "故障影响描述",
    "tag": ["tag1", "tag2"],
    "timelines": [{
        "id": 1,
        "startTime": "2025-08-15 12:00:00",
        "endTime":"",                                       // 选填
        "type": "occurred/discovered/mitigated/resolved",   // 选填
        "description": "发生了什么事情"
    }], 
    "improvements": [{
        "id": 1,
        "phase": "deploy",
        "problem": "1、问题\n2、问题",
        "todoList": [{
            "content": "整改措施",
            "owner":"整改负责人",
            "expected_time": "2025-08-15",
            "status": "pending"
        }],
    }],                                         // 改进项
    "attachments": [attchment_id]               // 附件
}
```

### 1.10 更新故障状态

- **URL**: `/incident/updateStatus`
- **方法**: `POST`
- **请求体**:

```json
{
    "id": 1,
    "status": "investigating"
}
```

- **响应**:

```json
{
    "code": 0,
    "message": "",
    "data": "ok"
}
```

### 1.11 附件上传

- **URL**: `/incident/uploadFile`
- **方法**: `FTP`
- **请求体**:

```file
```

- **响应**:

```json
{
    "code": 0,
    "message": "",
    "data": {
        "id": 123 // attchment_id
    }
}
```

### 1.12 列表查询

- **URL**: `/incident/list`
- **方法**: `GET`
- **请求体**:

```json
{
    "id": 1,
    "happenedAt": "2025-08-05",
    "nature": "good",
    "summary": "故障简述",
    "owner": "负责人",
    "level": "P0",
    "directCause": "直接原因",
    "rootCause": "根本原因",
    "impact": "故障影响描述",
    "autoStopLoss": true,
    "autoStopLossEffectived": false,
    "status": "created",
}
```

### 1.13 详情查询

- **URL**: `/incident/detail`
- **方法**: `GET`
- **请求体**:

```json
{
    "id": 1,
    "happenedAt": "2025-08-05",
    "nature": "good",
    "summary": "故障简述",
    "owner": "负责人",
    "level": "P0",
    "directCause": "直接原因",
    "rootCause": "根本原因",
    "impact": "故障影响描述",
    "autoStopLoss": true,
    "autoStopLossEffectived": false,
    "tag": ["tag1", "tag2"],
    "timelines": [{
        "id": 1,
        "startTime": "2025-08-15 12:00:00",
        "endTime":"",                                       // 选填
        "type": "occurred/discovered/mitigated/resolved",   // 选填
        "description": "发生了什么事情"
    }], 
    "improvements": [{
        "id": 1,
        "phase": "deploy",
        "problem": "1、问题\n2、问题",
        "todoList": [{
            "content": "整改措施",
            "owner":"整改负责人",
            "expected_time": "2025-08-15",
            "status": "pending"
        }],
    }],                                          // 改进项
    "attachments": [attchment_id],               // 附件
    "status": "created",
}
```

## 2. 仪表盘

### 2.1 接口人场景

#### 2.1.1 业务团队下拉列表

- **URL**: `/dashboard/business`
- **方法**: `GET`
- **请求体**: 无

- **响应**:

```json
{
    "code": 0,
    "message": "",
    "data": [{
        "team": "信贷团队",
        "business": ["用信稳定性", "贷后稳定性"]
    }, {
        "team": "支付团队",
        "business": ["支付稳定性1", "支付稳定性2"]
    }],
}
```

#### 2.1.2 获取接口人信息

- **URL**: `/dashboard/contact`
- **方法**: `GET`
- **说明**:
- **请求体**:

```queryString
?team=信贷团队&business=信贷稳定性1&startMonth=2025-07&endMonth=2025-07
```

- **响应**:

```json
{
    "code": 0,
    "message": "",
    "data": {
        "current": {
            "account": "huangchengjuan_dxm",
            "name": "黄成娟",
            "startTime": "2024-01-01 00:00:00",
            "endTime": "2025-01-01 00:00:00"
        },
        "history": [{
            "account": "zhaopinglan_dxm",
            "name": "赵平兰",
            "startTime": "2025-01-01 00:00:00",
            "endTime": ""
        }, {
            "account": "huangchengjuan_dxm",
            "name": "黄成娟",
            "startTime": "2024-01-01 00:00:00",
            "endTime": "2025-01-01 00:00:00"
        }, {
            "account": "yangfengxin_dxm",
            "name": "杨凤心",
            "startTime": "2023-01-01 00:00:00",
            "endTime": "2024-01-01 00:00:00"
        }]
    },
}
```

#### 2.1.3 获取指标信息(操作风险类)

- **URL**: `/dashboard/operation/aggregation`
- **方法**: `GET`
- **说明**：startMonth等于endMonth时，查看当月数据；team=all时聚合所有团队数据；bussiness=all时聚合团队数据;
- **请求体**:

```queryString
?team=信贷团队&business=信贷稳定性1&startMonth=2025-07&endMonth=2025-07
?team=信贷团队&business=all&startMonth=2025-07&endMonth=2025-07
?team=all&business=all&startMonth=2025-07&endMonth=2025-08
```

- **响应**:

```json
{
    "code": 0,
    "message": "",
    "data": {
        "total": 36,        // 操作故障次数
        "notReported": 2,   // 未通报
        "notChecked": 28,   // 未检查
        "notStandard": 44   // 不规范
    }
}
```

#### 2.1.4 获取指标趋势信息(操作风险类)

- **URL**: `/dashboard/operation/trend`
- **方法**: `GET`
- **说明**：startMonth等于endMonth时，查看当月数据；team=all时聚合所有团队数据；bussiness=all时聚合团队数据;
- **请求体**:

```queryString
?team=信贷团队&business=信贷稳定性1&startMonth=2025-07&endMonth=2025-07
?team=信贷团队&business=all&startMonth=2025-07&endMonth=2025-07
?team=all&business=all&startMonth=2025-07&endMonth=2025-08
```

- **响应**:

```json
{
    "code": 0,
    "message": "",
    "data": {
        "total": {
            "2025-04": 1,
            "2025-05": 2,
            "2025-06": 3,
            "2025-07": 2,
            "2025-08": 1
        }
    }
}
```

#### 2.1.5 获取指标信息(容量类)

- **URL**: `/dashboard/capacity/aggregation`
- **方法**: `GET`
- **说明**：startMonth等于endMonth时，查看当月数据；team=all时聚合所有团队数据；bussiness=all时聚合团队数据;
- **请求体**:

```queryString
?team=信贷团队&business=信贷稳定性1&startMonth=2025-07&endMonth=2025-07
?team=信贷团队&business=all&startMonth=2025-07&endMonth=2025-07
?team=all&business=all&startMonth=2025-07&endMonth=2025-08
```

- **响应**:

```json
{
    "code": 0,
    "message": "",
    "data": {
        "coverage": 49.27,      // 容量平台覆盖率
        "successRate": 88.22,   // 扩容成功率
        "efficiency": 3.28,     // 扩容效率（单位 次/h）
        "averageHours": 0.62    // 平均审批时长
    }
}
```

#### 2.1.6 获取指标趋势信息(容量类)

- **URL**: `/dashboard/capacity/trend`
- **方法**: `GET`
- **说明**：startMonth等于endMonth时，查看当月数据；team=all时聚合所有团队数据；bussiness=all时聚合团队数据;
- **请求体**:

```queryString
?team=信贷团队&business=信贷稳定性1&startMonth=2025-07&endMonth=2025-07
?team=信贷团队&business=all&startMonth=2025-07&endMonth=2025-07
?team=all&business=all&startMonth=2025-07&endMonth=2025-08
```

- **响应**:

```json
{
    "code": 0,
    "message": "",
    "data": {
        "coverage": {
            "2025-04": 49.27,
            "2025-05": 49.27,
            "2025-06": 49.27,
            "2025-07": 49.27,
            "2025-08": 49.27
        },
        "successRate": {
            "2025-04": 49.27,
            "2025-05": 49.27,
            "2025-06": 49.27,
            "2025-07": 49.27,
            "2025-08": 49.27
        },
        "efficiency": {
            "2025-04": 49.27,
            "2025-05": 49.27,
            "2025-06": 49.27,
            "2025-07": 49.27,
            "2025-08": 49.27
        },
        "averageHours": {
            "2025-04": 49.27,
            "2025-05": 49.27,
            "2025-06": 49.27,
            "2025-07": 49.27,
            "2025-08": 49.27
        } 
    }
}
```

#### 2.1.7 获取指标信息(部署类)

- **URL**: `/dashboard/deployment/aggregation`
- **方法**: `GET`
- **说明**：startMonth等于endMonth时，查看当月数据；team=all时聚合所有团队数据；bussiness=all时聚合团队数据;
- **请求体**:

```queryString
?team=信贷团队&business=信贷稳定性1&startMonth=2025-07&endMonth=2025-07
?team=信贷团队&business=all&startMonth=2025-07&endMonth=2025-07
?team=all&business=all&startMonth=2025-07&endMonth=2025-08
```

- **响应**:

```json
{
    "code": 0,
    "message": "",
    "data": {
        "failureRate": 7.17,                    // 上线导致的故障率
        "rollbackRate": 8.29,                   // 上线回滚率
        "rollbackList": [{                      // 回滚Top10（已按回滚率排序
            "name": "test_app.test_product",    // BNS
            "value": 49.12                      // 回滚率
        }, {
            "name": "test_app.test_product",
            "value": 43.20
        }]
    }
}
```

#### 2.1.8 获取指标趋势信息(部署类)

- **URL**: `/dashboard/deployment/trend`
- **方法**: `GET`
- **说明**：startMonth等于endMonth时，查看当月数据；team=all时聚合所有团队数据；bussiness=all时聚合团队数据;
- **请求体**:

```queryString
?team=信贷团队&business=信贷稳定性1&startMonth=2025-07&endMonth=2025-07
?team=信贷团队&business=all&startMonth=2025-07&endMonth=2025-07
?team=all&business=all&startMonth=2025-07&endMonth=2025-08
```

- **响应**:

```json
{
    "code": 0,
    "message": "",
    "data": {
        "failureRate": {
            "2025-04": 7.17,
            "2025-05": 7.17,
            "2025-06": 7.17,
            "2025-07": 7.17,
            "2025-08": 7.17
        },
        "rollbackRate": {
            "2025-04": 8.29,
            "2025-05": 8.29,
            "2025-06": 8.29,
            "2025-07": 8.29,
            "2025-08": 8.29
        }
    }
}
```

#### 2.1.9 获取指标信息(监控类)

- **URL**: `/dashboard/monitor/aggregation`
- **方法**: `GET`
- **说明**：startMonth等于endMonth时，查看当月数据；team=all时聚合所有团队数据；bussiness=all时聚合团队数据;
- **请求体**:

```queryString
?team=信贷团队&business=信贷稳定性1&startMonth=2025-07&endMonth=2025-07
?team=信贷团队&business=all&startMonth=2025-07&endMonth=2025-07
?team=all&business=all&startMonth=2025-07&endMonth=2025-08
```

- **响应**:

```json
{
    "code": 0,
    "message": "",
    "data": {
        "machineCoverage": 63.51,               // 机器监控覆盖率
        "bnsCoverage": 88.22,                   // BNS监控覆盖率
        "crossDepartmentBreachedRate": 41.61,   // 跨部门服务SLA达标率
        "crossDepartmentTotal": 44,             // 跨部分服务总数
        "crossDepartmentBuilt": 32,             // 跨部门服务SLA建成数
        "recalled": 28,                         // 监控召回率
        "notRecalled": {                        // 未召回故障次数
            "p0": 28,                           // - P0
            "p1": 46                            // - P1
        },
    }
}
```

#### 2.1.10 获取指标趋势信息(监控类)

- **URL**: `/dashboard/monitor/trend`
- **方法**: `GET`
- **说明**：startMonth等于endMonth时，查看当月数据；team=all时聚合所有团队数据；bussiness=all时聚合团队数据;
- **请求体**:

```queryString
?team=信贷团队&business=信贷稳定性1&startMonth=2025-07&endMonth=2025-07
?team=信贷团队&business=all&startMonth=2025-07&endMonth=2025-07
?team=all&business=all&startMonth=2025-07&endMonth=2025-08
```

- **响应**:

```json
{
    "code": 0,
    "message": "",
    "data": {
        "machineCoverage": {
            "2025-04": 63.51,
            "2025-05": 63.51,
            "2025-06": 63.51,
            "2025-07": 63.51,
            "2025-08": 63.51
        },
        "bnsCoverage": {
            "2025-04": 88.22,
            "2025-05": 88.22,
            "2025-06": 88.22,
            "2025-07": 88.22,
            "2025-08": 88.22
        },
        "crossDepartmentBreachedRate": {
            "2025-04": 41.61,
            "2025-05": 41.61,
            "2025-06": 41.61,
            "2025-07": 41.61,
            "2025-08": 41.61
        },
        "recalledRate": {
            "2025-04": 41.61,
            "2025-05": 41.61,
            "2025-06": 41.61,
            "2025-07": 41.61,
            "2025-08": 41.61
        },
        "notRecalled": {
            "2025-04": 1,
            "2025-05": 2,
            "2025-06": 3,
            "2025-07": 4,
            "2025-08": 5
        }
    }
}
```

#### 2.1.11 获取指标信息(预案平台类)

- **URL**: `/dashboard/plan/aggregation`
- **方法**: `GET`
- **说明**：startMonth等于endMonth时，查看当月数据；team=all时聚合所有团队数据；bussiness=all时聚合团队数据;
- **请求体**:

```queryString
?team=信贷团队&business=信贷稳定性1&startMonth=2025-07&endMonth=2025-07
?team=信贷团队&business=all&startMonth=2025-07&endMonth=2025-07
?team=all&business=all&startMonth=2025-07&endMonth=2025-08
```

- **响应**:

```json
{
    "code": 0,
    "message": "",
    "data": {
        "coverage": 63.51,          // 预案覆盖率
        "failureRate": 88.22,       // 预案执行失败率
        "emptyRate": 41.61,         // 切空率
        "emptyList": [{             // 切空率列表
            "productLine": "信贷团队",
            "value": 49.12
        }, {
            "productLine": "金科团队",
            "value": 43.20
        }]
    }
}
```

#### 2.1.12 获取指标趋势信息(预案平台类)

- **URL**: `/dashboard/plan/trend`
- **方法**: `GET`
- **说明**：startMonth等于endMonth时，查看当月数据；team=all时聚合所有团队数据；bussiness=all时聚合团队数据;
- **请求体**:

```queryString
?team=信贷团队&business=信贷稳定性1&startMonth=2025-07&endMonth=2025-07
?team=信贷团队&business=all&startMonth=2025-07&endMonth=2025-07
?team=all&business=all&startMonth=2025-07&endMonth=2025-08
```

- **响应**:

```json
{
    "code": 0,
    "message": "",
    "data": {
        "coverage": {
            "2025-04": 63.51,
            "2025-05": 63.51,
            "2025-06": 63.51,
            "2025-07": 63.51,
            "2025-08": 63.51
        },
        "failureRate": {
            "2025-04": 88.22,
            "2025-05": 88.22,
            "2025-06": 88.22,
            "2025-07": 88.22,
            "2025-08": 88.22
        },
        "emptyRate": {
            "2025-04": 41.61,
            "2025-05": 41.61,
            "2025-06": 41.61,
            "2025-07": 41.61,
            "2025-08": 41.61
        }
    }
}
```
